@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                    Starting Clonie Web App                   ║
echo ╠═══════════════════════════════════════════════════════════════╣
echo ║   🎙️  OpenVoice V2 Voice Cloning Studio                     ║
echo ║   🚀  React Frontend + Python FastAPI Backend               ║
echo ║   📜  MIT Licensed - Free for commercial use                 ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

echo 📦 Checking backend dependencies...
cd backend
echo 🔍 Verifying OpenVoice V2 dependencies are compatible...
python -c "import huggingface_hub; print('✅ huggingface-hub:', huggingface_hub.__version__)"
if %errorlevel% neq 0 (
    echo 📦 Installing backend dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Failed to install backend dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ Backend dependencies already installed and compatible
)

echo.
echo 📥 Checking OpenVoice V2 models...
python -c "import os; from pathlib import Path; exit(0 if (Path('models/openvoice/checkpoints_v2/converter').exists()) else 1)"
if %errorlevel% neq 0 (
    echo 📥 Downloading OpenVoice V2 models...
    python download_openvoice_v2.py
    if %errorlevel% neq 0 (
        echo ❌ Failed to download OpenVoice V2 models
        pause
        exit /b 1
    )
) else (
    echo ✅ OpenVoice V2 models already downloaded
)

echo.
echo 📦 Installing frontend dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo 🔍 Testing OpenVoice V2 imports...
cd ..\backend
python -c "from openvoice_v2_cloning import _ensure_openvoice_imports; success = _ensure_openvoice_imports(); exit(0 if success else 1)"
if %errorlevel% neq 0 (
    echo ❌ OpenVoice V2 imports failed. Please check dependencies.
    pause
    exit /b 1
) else (
    echo ✅ OpenVoice V2 imports successful
)

echo.
echo 🚀 Starting backend server...
start "Clonie Backend" cmd /k "python main.py"

echo.
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak > nul

echo.
echo 🌐 Starting frontend development server...
cd ..\frontend
start "Clonie Frontend" cmd /k "npm run dev"

echo.
echo ✅ Clonie with OpenVoice V2 is starting up!
echo.
echo 📱 Frontend will be available at: http://localhost:5173
echo 🔧 Backend API will be available at: http://localhost:8000
echo.
echo 🎙️ OpenVoice V2 Features:
echo    • 6 Languages: English, Spanish, French, Chinese, Japanese, Korean
echo    • Commercial Use: MIT Licensed - completely free
echo    • High Quality: Two-stage architecture for superior voice cloning
echo.
echo 💡 Both servers are running in separate windows.
echo    Close those windows to stop the servers.
echo.
pause
